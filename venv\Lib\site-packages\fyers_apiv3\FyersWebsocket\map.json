{"data_val": ["ltp", "vol_traded_today", "last_traded_time", "exch_feed_time", "bid_size", "ask_size", "bid_price", "ask_price", "last_traded_qty", "tot_buy_qty", "tot_sell_qty", "avg_trade_price", "OI", "low_price", "high_price", "Yhigh", "<PERSON><PERSON>", "lower_ckt", "upper_ckt", "open_price", "prev_close_price", "type", "symbol"], "index_val": ["ltp", "prev_close_price", "exch_feed_time", "high_price", "low_price", "open_price", "type", "symbol"], "lite_val": ["ltp", "symbol", "type"], "depthvalue": ["bid_price1", "bid_price2", "bid_price3", "bid_price4", "bid_price5", "ask_price1", "ask_price2", "ask_price3", "ask_price4", "ask_price5", "bid_size1", "bid_size2", "bid_size3", "bid_size4", "bid_size5", "ask_size1", "ask_size2", "ask_size3", "ask_size4", "ask_size5", "bid_order1", "bid_order2", "bid_order3", "bid_order4", "bid_order5", "ask_order1", "ask_order2", "ask_order3", "ask_order4", "ask_order5", "type", "symbol"], "index_dict": {"NSE:NIFTYINDIAMFG-INDEX": "NIFTY INDIA MFG", "NSE:NIFTY100ESG-INDEX": "NIFTY100 ESG", "NSE:NIFTYINDDIGITAL-INDEX": "NIFTY IND DIGITAL", "NSE:NIFTYMICROCAP250-INDEX": "NIFTY MICROCAP250", "NSE:NIFTYCONSRDURBL-INDEX": "NIFTY CONSR DURBL", "NSE:NIFTYHEALTHCARE-INDEX": "NIFTY HEALTHCARE", "NSE:NIFTYOILANDGAS-INDEX": "NIFTY OIL AND GAS", "NSE:NIFTY100ESGSECLDR-INDEX": "Nifty100ESGSecLdr", "NSE:NIFTY200MOMENTM30-INDEX": "Nifty200Momentm30", "NSE:NIFTYALPHALOWVOL-INDEX": "NIFTY AlphaLowVol", "NSE:NIFTY200QUALTY30-INDEX": "NIFTY200 QUALTY30", "NSE:NIFTYSMLCAP50-INDEX": "NIFTY SMLCAP 50", "NSE:MIDCPNIFTY-INDEX": "NIFTY MID SELECT", "NSE:NIFTYMIDSELECT-INDEX": "NIFTY MID SELECT", "NSE:NIFTYMIDCAP150-INDEX": "NIFTY MIDCAP 150", "NSE:NIFTY100 EQL WGT-INDEX": "NIFTY100 EQL Wgt", "NSE:NIFTY50 EQL WGT-INDEX": "NIFTY50 EQL Wgt", "NSE:NIFTYGSCOMPSITE-INDEX": "Nifty GS Compsite", "NSE:NIFTYGS1115YR-INDEX": "Nifty GS 11 15Yr", "NSE:NIFTYGS48YR-INDEX": "Nifty GS 4 8Yr", "NSE:NIFTYGS10YRCLN-INDEX": "Nifty GS 10Yr Cln", "NSE:NIFTYGS813YR-INDEX": "Nifty GS 8 13Yr", "NSE:NIFTYSMLCAP100-INDEX": "NIFTY SMLCAP 100", "NSE:NIFTYQUALITY30-INDEX": "NIFTY100 Qualty30", "NSE:NIFTYPVTBANK-INDEX": "Nifty Pvt Bank", "NSE:NIFTYPHARMA-INDEX": "<PERSON><PERSON>", "NSE:NIFTYLARGEMID250-INDEX": "NIFTY LARGEMID250", "NSE:NIFTYGS15YRPLUS-INDEX": "Nifty GS 15YrPlus", "NSE:NIFTYPSUBANK-INDEX": "Nifty PSU Bank", "NSE:NIFTYSMLCAP250-INDEX": "NIFTY SMLCAP 250", "NSE:NIFTYENERGY-INDEX": "Nifty Energy", "NSE:NIFTYALPHA50-INDEX": "NIFTY Alpha 50", "NSE:NIFTYPSE-INDEX": "Nifty PSE", "NSE:NIFTYFINSRV2550-INDEX": "Nifty FinSrv25 50", "NSE:FINNIFTY-INDEX": "Nifty Fin Service", "NSE:NIFTYREALTY-INDEX": "Nifty Realty", "NSE:NIFTY500-INDEX": "Nifty 500", "NSE:NIFTY500MULTICAP-INDEX": "NIFTY500 MULTICAP", "NSE:NIFTYMIDCAP50-INDEX": "Nifty Midcap 50", "NSE:NIFTYTOTALMKT-INDEX": "NIFTY TOTAL MKT", "NSE:NIFTY50PR2XLEV-INDEX": "Nifty50 PR 2x Lev", "NSE:INDIAVIX-INDEX": "India VIX", "NSE:NIFTYDIVOPPS50-INDEX": "Nifty Div Opps 50", "NSE:NIFTYMNC-INDEX": "Nifty MNC", "NSE:NIFTY50VALUE20-INDEX": "Nifty50 Value 20", "NSE:NIFTY50-INDEX": "Nifty 50", "NSE:HANGSENG BEES-NAV-INDEX": "HangSeng BeES-NAV", "NSE:NIFTY100LIQ15-INDEX": "Nifty100 Liq 15", "NSE:NIFTY50TR2XLEV-INDEX": "Nifty50 TR 2x Lev", "NSE:NIFTY100-INDEX": "Nifty 100", "NSE:NIFTY100 LOWVOL30-INDEX": "NIFTY100 LowVol30", "NSE:NIFTYBANK-INDEX": "Nifty Bank", "NSE:NIFTYFMCG-INDEX": "Nifty FMCG", "NSE:NIFTYIT-INDEX": "Nifty IT", "NSE:NIFTYGS10YR-INDEX": "Nifty GS 10Yr", "NSE:NIFTYMIDCAP100-INDEX": "NIFTY MIDCAP 100", "NSE:NIFTYNEXT50-INDEX": "Nifty Next 50", "NSE:NIFTYNXT50-INDEX": "Nifty Next 50", "NSE:NIFTYM150QLTY50-INDEX": "NIFTY M150 QLTY50", "NSE:NIFTYSERVSECTOR-INDEX": "Nifty Serv Sector", "NSE:NIFTYMIDSML400-INDEX": "NIFTY MIDSML 400", "NSE:NIFTYAUTO-INDEX": "Nifty Auto", "NSE:NIFTYMETAL-INDEX": "Nifty Metal", "NSE:NIFTYINFRA-INDEX": "Nifty Infra", "NSE:NIFTYMEDIA-INDEX": "Nifty Media", "NSE:NIFTY50PR1XINV-INDEX": "Nifty50 PR 1x Inv", "NSE:NIFTY200-INDEX": "Nifty 200", "NSE:NIFTY50TR1XINV-INDEX": "Nifty50 TR 1x Inv", "NSE:NIFTYCPSE-INDEX": "Nifty CPSE", "NSE:NIFTYMIDLIQ15-INDEX": "Nifty Mid Liq 15", "NSE:NIFTYCOMMODITIES-INDEX": "Nifty Commodities", "NSE:NIFTYCONSUMPTION-INDEX": "Nifty Consumption", "NSE:NIFTY50DIVPOINT-INDEX": "Nifty50 Div Point", "NSE:NIFTYGROWSECT15-INDEX": "Nifty GrowSect 15", "BSE:100LARGECAPTMC-INDEX": "LCTMCI", "BSE:DFRG-INDEX": "DFRGRI", "BSE:QUALITY-INDEX": "BSEQUI", "BSE:DIVIDENDSTABILITY-INDEX": "BSEDSI", "BSE:250SMALLCAP-INDEX": "SML250", "BSE:150MIDCAP-INDEX": "MID150", "BSE:ESG100-INDEX": "ESG100", "BSE:SNXT50-INDEX": "SNXT50", "BSE:SNSX50-INDEX": "SNSX50", "BSE:UTILS-INDEX": "UTILS", "BSE:GREENEX-INDEX": "GREENX", "BSE:SENSEX-INDEX": "SENSEX", "BSE:REALTY-INDEX": "REALTY", "BSE:PRIVATEBANKS-INDEX": "BSEPBI", "BSE:CDGS-INDEX": "CDGS", "BSE:OILGAS-INDEX": "OILGAS", "BSE:ENERGY-INDEX": "ENERGY", "BSE:POWER-INDEX": "POWER", "BSE:500-INDEX": "BSE500", "BSE:100-INDEX": "BSE100", "BSE:PSU-INDEX": "BSEPSU", "BSE:HC-INDEX": "BSE HC", "BSE:400MIDSMALLCAP-INDEX": "MSL400", "BSE:BHRT22-INDEX": "BHRT22", "BSE:BANKEX-INDEX": "BANKEX", "BSE:ALLCAP-INDEX": "ALLCAP", "BSE:INFRA-INDEX": "INFRA", "BSE:CD-INDEX": "BSE CD", "BSE:MIDCAP-INDEX": "MIDCAP", "BSE:AUTO-INDEX": "AUTO", "BSE:BASMTR-INDEX": "BASMTR", "BSE:200-INDEX": "BSE200", "BSE:FIN-INDEX": "FIN", "BSE:CG-INDEX": "BSE CG", "BSE:ENHANCEDVALUE-INDEX": "BSEEVI", "BSE:TECK-INDEX": "TECK", "BSE:METAL-INDEX": "METAL", "BSE:CARBONEX-INDEX": "CARBON", "BSE:MIDSEL-INDEX": "MIDSEL", "BSE:SME IPO-INDEX": "SMEIPO", "BSE:MOMENTUM-INDEX": "BSEMOI", "BSE:TELCOM-INDEX": "TELCOM", "BSE:CPSE-INDEX": "CPSE", "BSE:250LARGEMIDCAP-INDEX": "LMI250", "BSE:SMLCAP-INDEX": "SMLCAP", "BSE:IT-INDEX": "BSE IT", "BSE:INDIAMANUFACTURING-INDEX": "MFG", "BSE:INDSTR-INDEX": "INDSTR", "BSE:LOWVOLATILITY-INDEX": "BSELVI", "BSE:LRGCAP-INDEX": "LRGCAP", "BSE:IPO-INDEX": "BSEIPO", "BSE:FMC-INDEX": "BSEFMC", "BSE:SMLSEL-INDEX": "SMLSEL"}, "exch_seg_dict": {"1010": "nse_cm", "1011": "nse_fo", "1120": "mcx_fo", "1210": "bse_cm", "1012": "cde_fo", "1211": "bse_fo", "1212": "bcs_fo", "1020": "nse_com"}, "position_mapper": {"symbol": "symbol", "id": "id", "buy_avg": "buyAvg", "buy_qty": "buyQty", "buy_val": "buyVal", "sell_avg": "sellAvg", "sell_qty": "sellQty", "sell_val": "sellVal", "net_avg": "netAvg", "net_qty": "netQty", "tran_side": "side", "qty": "qty", "product_type": "productType", "pl_realized": "realized_profit", "rbirefrate": "rbiRefRate", "fy_token": "fyToken", "exchange": "exchange", "segment": "segment", "day_buy_qty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "day_sell_qty": "day<PERSON><PERSON><PERSON><PERSON>", "cf_buy_qty": "cfBuyQty", "cf_sell_qty": "cfSellQty", "qty_multiplier": "qtyMulti_com", "pl_total": "pl", "cross_curr_flag": "crossCurrency", "pl_unrealized": "unrealized_profit"}, "order_mapper": {"client_id": "clientId", "id": "id", "id_parent": "parentId", "id_exchange": "exchOrdId", "qty": "qty", "qty_remaining": "remainingQuantity", "qty_filled": "filledQty", "price_limit": "limitPrice", "price_stop": "stopPrice", "price_traded": "tradedPrice", "ord_type": "type", "fy_token": "fyToken", "exchange": "exchange", "segment": "segment", "symbol": "symbol", "instrument": "instrument", "oms_msg": "message", "offline_flag": "offlineOrder", "time_oms": "orderDateTime", "validity": "orderValidity", "product_type": "productType", "tran_side": "side", "org_ord_status": "status", "ord_source": "source", "symbol_exch": "ex_sym", "symbol_desc": "description", "ordertag": "orderTag"}, "trade_mapper": {"id_fill": "tradeNumber", "id": "orderNumber", "qty_traded": "tradedQty", "price_traded": "tradePrice", "traded_val": "tradeValue", "product_type": "productType", "client_id": "clientId", "id_exchange": "exchangeOrderNo", "ord_type": "orderType", "tran_side": "side", "symbol": "symbol", "fill_time": "orderDateTime", "fy_token": "fyToken", "exchange": "exchange", "segment": "segment", "ordertag": "orderTag"}}